extends CharacterBody2D
class_name Player

# Movement properties
@export var base_speed: float = 200.0
@export var dash_speed: float = 500.0
@export var dash_duration: float = 0.2
@export var dash_cooldown: float = 1.0

# Animation signals
signal animation_changed(animation_name: String)
signal dash_started
signal dash_ended

# Internal variables
var current_speed: float
var is_dashing: bool = false
var can_dash: bool = true
var dash_direction: Vector2
var dash_timer: float = 0.0
var cooldown_timer: float = 0.0

# Animation states
enum AnimationState {
	IDLE,
	RUN,
	DASH
}

var current_animation_state: AnimationState = AnimationState.IDLE

func _ready():
	current_speed = base_speed

func _physics_process(delta):
	handle_input()
	handle_dash(delta)
	handle_movement(delta)
	update_animation_state()
	move_and_slide()

func handle_input():
	# Get movement input
	var input_vector = Vector2.ZERO

	if Input.is_action_pressed("move_up"):
		input_vector.y -= 1
	if Input.is_action_pressed("move_down"):
		input_vector.y += 1
	if Input.is_action_pressed("move_left"):
		input_vector.x -= 1
	if Input.is_action_pressed("move_right"):
		input_vector.x += 1

	# Normalize diagonal movement
	if input_vector.length() > 0:
		input_vector = input_vector.normalized()

	# Handle dash input
	if Input.is_action_just_pressed("dash") and can_dash and input_vector.length() > 0:
		start_dash(input_vector)

	# Set velocity for normal movement (dash overrides this)
	if not is_dashing:
		velocity = input_vector * current_speed

func start_dash(direction: Vector2):
	is_dashing = true
	can_dash = false
	dash_direction = direction
	dash_timer = dash_duration
	cooldown_timer = dash_cooldown
	current_speed = dash_speed

	dash_started.emit()
	print("Dash started!")

func handle_dash(delta):
	if is_dashing:
		dash_timer -= delta
		velocity = dash_direction * dash_speed

		if dash_timer <= 0:
			end_dash()

	# Handle cooldown
	if not can_dash:
		cooldown_timer -= delta
		if cooldown_timer <= 0:
			can_dash = true

func end_dash():
	is_dashing = false
	current_speed = base_speed
	dash_ended.emit()
	print("Dash ended!")

func handle_movement(delta):
	# This function can be extended for additional movement logic
	pass

func update_animation_state():
	var new_state: AnimationState

	if is_dashing:
		new_state = AnimationState.DASH
	elif velocity.length() > 0:
		new_state = AnimationState.RUN
	else:
		new_state = AnimationState.IDLE

	if new_state != current_animation_state:
		current_animation_state = new_state
		emit_animation_signal()

func emit_animation_signal():
	var animation_name: String

	match current_animation_state:
		AnimationState.IDLE:
			animation_name = "idle"
		AnimationState.RUN:
			animation_name = "run"
		AnimationState.DASH:
			animation_name = "dash"

	animation_changed.emit(animation_name)

# Getter functions for UI/other systems
func get_dash_cooldown_progress() -> float:
	if can_dash:
		return 1.0
	return 1.0 - (cooldown_timer / dash_cooldown)

func is_dash_ready() -> bool:
	return can_dash