{"available_traits": [{"id": "warrior_strength", "name": "Warrior's Strength", "description": "+5% physical damage", "type": "damage_bonus", "stat_modifiers": {"finesse": 2}, "special_effects": ["physical_damage_bonus_5"]}, {"id": "mage_wisdom", "name": "<PERSON><PERSON>'s Wisdom", "description": "+5% magic damage", "type": "damage_bonus", "stat_modifiers": {"aether": 2}, "special_effects": ["magic_damage_bonus_5"]}, {"id": "crafters_eye", "name": "Crafter's Eye", "description": "+10% crafting XP and better loot quality", "type": "crafting_bonus", "stat_modifiers": {"craft": 3}, "special_effects": ["crafting_xp_bonus_10", "loot_quality_bonus"]}, {"id": "survivors_instinct", "name": "Survivor's Instinct", "description": "+15% maximum health", "type": "health_bonus", "stat_modifiers": {"vitalis": 3}, "special_effects": ["max_health_bonus_15"]}, {"id": "swift_reflexes", "name": "Swift Reflexes", "description": "+10% movement speed and dash cooldown reduction", "type": "mobility_bonus", "stat_modifiers": {"finesse": 2}, "special_effects": ["movement_speed_bonus_10", "dash_cooldown_reduction"]}, {"id": "arcane_affinity", "name": "Arcane Affinity", "description": "+20% skill cooldown reduction", "type": "skill_bonus", "stat_modifiers": {"aether": 1}, "special_effects": ["skill_cooldown_reduction_20"]}, {"id": "iron_will", "name": "Iron Will", "description": "+25% resistance to all damage", "type": "defense_bonus", "stat_modifiers": {"resolve": 4}, "special_effects": ["damage_resistance_25"]}, {"id": "charismatic_leader", "name": "Charismatic Leader", "description": "Better NPC interactions and shop prices", "type": "social_bonus", "stat_modifiers": {"presence": 4}, "special_effects": ["shop_discount_15", "npc_bonus_interactions"]}, {"id": "treasure_hunter", "name": "Treasure Hunter", "description": "+25% chance to find rare loot", "type": "loot_bonus", "stat_modifiers": {"craft": 2}, "special_effects": ["rare_loot_chance_25"]}, {"id": "battle_hardened", "name": "Battle Hardened", "description": "+10% critical hit chance", "type": "combat_bonus", "stat_modifiers": {"finesse": 2}, "special_effects": ["crit_chance_bonus_10"]}, {"id": "mystic_scholar", "name": "Mystic Scholar", "description": "Start with an extra skill point", "type": "skill_bonus", "stat_modifiers": {"aether": 1}, "special_effects": ["extra_skill_point"]}, {"id": "lucky_charm", "name": "Lucky Charm", "description": "+15% XP gain from all sources", "type": "experience_bonus", "stat_modifiers": {}, "special_effects": ["xp_bonus_15"]}], "legacy_data": {"current_run": 1, "total_deaths": 0, "active_traits": [], "unlocked_traits": ["warrior_strength", "mage_wisdom", "crafters_eye", "survivors_instinct"], "run_history": []}}