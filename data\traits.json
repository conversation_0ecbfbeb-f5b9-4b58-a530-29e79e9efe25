Create a 2D top-down player movement script in Godot using GDScript. The player should be able to move with WASD and dash in any direction using Shift. Add a cooldown to the dash and simple animation signals for idle, run, and dash.

Build a stat system in Godot (GDScript) for an RPG game with the following attributes: <PERSON><PERSON> (HP), <PERSON><PERSON> (Speed/Crit), <PERSON><PERSON><PERSON> (Magic Power), <PERSON>solve (Resistance), Craft (Loot Quality), and Presence (NPC Interactions). Each stat should affect gameplay and be visible in a stat panel UI.

Create a skill system in Godot using GDScript where each character has 3 active skills, 2 passives, and 1 ultimate. Each skill should be defined in a resource file with attributes like damage, cooldown, cost, and FX. Include hotkey handling (Q/E/R/F) to trigger skills with cooldowns.

Design an inventory system in Godot where players can pick up loot and manage items via drag-and-drop. Items should have types (weapon, armor, relic), rarity (common to mythic), and optional modifiers. Include basic UI with item tooltips.

Implement a permadeath system in Godot with a legacy mode. When the player dies, they can choose 1 or 2 traits to pass to the next run (e.g., +5% damage, +10% crafting XP). These should be stored in a JSON file and loaded on new runs.

Create a procedural room-based dungeon generator in Godot using GDScript. Rooms should connect logically and support themes (e.g., jungle, ruins). Include enemy spawn points and loot chest locations. Use a seed for repeatable generation.

Build a crafting system in Godot where players can combine materials at a forge to create or upgrade gear. Include a UI for recipe selection, previewing results, and checking required materials. Materials should be dropped during exploration.

Create a loot drop system with weighted probabilities in Godot. Items can have modifiers, rarity levels, and class alignment. The system should return drops based on enemy level and player Craft stat.

Add an ultimate skill system to Godot where each class can activate a Totem Surge — a powerful spirit transformation with visual effects and buffs. It should have a charge meter that fills during combat. Triggered by pressing Spacebar.

Implement a minimap with fog of war in Godot. The map should reveal rooms as the player explores them and mark important elements like chests and exit portals. Include a keybind to toggle the full map.
