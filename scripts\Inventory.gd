extends Node
class_name Inventory

# Inventory signals
signal item_added(item: Item, quantity: int)
signal item_removed(item: Item, quantity: int)
signal item_equipped(item: Item, slot: String)
signal item_unequipped(item: Item, slot: String)
signal inventory_full()

# Inventory structure
class InventorySlot:
	var item: Item = null
	var quantity: int = 0

	func _init(item_ref: Item = null, qty: int = 0):
		item = item_ref
		quantity = qty

	func is_empty() -> bool:
		return item == null or quantity <= 0

	func can_add_item(new_item: Item, qty: int) -> bool:
		if is_empty():
			return true
		if item.can_stack_with(new_item):
			return quantity + qty <= item.stack_size
		return false

	func add_item(new_item: Item, qty: int) -> int:
		if is_empty():
			item = new_item
			quantity = qty
			return 0
		elif item.can_stack_with(new_item):
			var space_available = item.stack_size - quantity
			var amount_to_add = min(qty, space_available)
			quantity += amount_to_add
			return qty - amount_to_add
		return qty

	func remove_item(qty: int) -> int:
		var amount_removed = min(quantity, qty)
		quantity -= amount_removed
		if quantity <= 0:
			item = null
			quantity = 0
		return amount_removed

# Inventory data
@export var max_slots: int = 30
var slots: Array[InventorySlot] = []

# Equipment slots
var equipped_items: Dictionary = {
	"weapon": null,
	"armor": null,
	"accessory": null
}

# References
var stats: Stats

func _ready():
	initialize_inventory()

func initialize_inventory():
	slots.clear()
	for i in range(max_slots):
		slots.append(InventorySlot.new())

func setup(stats_ref: Stats):
	stats = stats_ref

func add_item(item: Item, quantity: int = 1) -> bool:
	var remaining_quantity = quantity

	# First, try to stack with existing items
	for slot in slots:
		if not slot.is_empty() and slot.item.can_stack_with(item):
			remaining_quantity = slot.add_item(item, remaining_quantity)
			if remaining_quantity <= 0:
				item_added.emit(item, quantity)
				return true

	# Then, try to add to empty slots
	for slot in slots:
		if slot.is_empty():
			remaining_quantity = slot.add_item(item, remaining_quantity)
			if remaining_quantity <= 0:
				item_added.emit(item, quantity)
				return true

	# If we still have items left, inventory is full
	if remaining_quantity > 0:
		inventory_full.emit()
		return false

	item_added.emit(item, quantity)
	return true

func remove_item(item: Item, quantity: int = 1) -> int:
	var total_removed = 0

	for slot in slots:
		if not slot.is_empty() and slot.item.item_name == item.item_name:
			var removed = slot.remove_item(quantity - total_removed)
			total_removed += removed

			if total_removed >= quantity:
				break

	if total_removed > 0:
		item_removed.emit(item, total_removed)

	return total_removed

func has_item(item: Item, quantity: int = 1) -> bool:
	var total_count = 0

	for slot in slots:
		if not slot.is_empty() and slot.item.item_name == item.item_name:
			total_count += slot.quantity
			if total_count >= quantity:
				return true

	return false

func get_item_count(item: Item) -> int:
	var total_count = 0

	for slot in slots:
		if not slot.is_empty() and slot.item.item_name == item.item_name:
			total_count += slot.quantity

	return total_count

func equip_item(item: Item) -> bool:
	if item.item_type != Item.ItemType.WEAPON and item.item_type != Item.ItemType.ARMOR and item.item_type != Item.ItemType.RELIC:
		print("Cannot equip this item type")
		return false

	var slot_name = item.equipment_slot
	if slot_name == "":
		print("Item has no equipment slot defined")
		return false

	# Check level requirement
	if stats and stats.level < item.level_requirement:
		print("Level requirement not met")
		return false

	# Unequip current item if any
	if equipped_items[slot_name] != null:
		unequip_item(slot_name)

	# Remove from inventory
	if not has_item(item):
		print("Item not in inventory")
		return false

	remove_item(item, 1)

	# Equip the item
	equipped_items[slot_name] = item
	apply_item_stats(item, true)
	item_equipped.emit(item, slot_name)

	return true

func unequip_item(slot_name: String) -> bool:
	if slot_name not in equipped_items or equipped_items[slot_name] == null:
		return false

	var item = equipped_items[slot_name]

	# Try to add back to inventory
	if not add_item(item):
		print("Inventory full, cannot unequip item")
		return false

	# Remove item stats
	apply_item_stats(item, false)

	equipped_items[slot_name] = null
	item_unequipped.emit(item, slot_name)

	return true

func apply_item_stats(item: Item, equipping: bool):
	if not stats:
		return

	var multiplier = 1 if equipping else -1

	for stat_name in item.stat_modifiers:
		var value = item.stat_modifiers[stat_name] * multiplier
		stats.add_stat_modifier(stat_name, value)

func use_consumable(item: Item) -> bool:
	if item.item_type != Item.ItemType.CONSUMABLE:
		return false

	if not has_item(item):
		return false

	# Apply consumable effect
	match item.consumable_effect:
		"heal":
			if stats:
				stats.heal(item.effect_value)
		"restore_aether":
			# Would implement aether restoration
			pass
		_:
			print("Unknown consumable effect: " + item.consumable_effect)

	remove_item(item, 1)
	return true

func get_empty_slot_count() -> int:
	var count = 0
	for slot in slots:
		if slot.is_empty():
			count += 1
	return count

func is_full() -> bool:
	return get_empty_slot_count() == 0

func get_all_items() -> Array[Item]:
	var items: Array[Item] = []
	for slot in slots:
		if not slot.is_empty():
			items.append(slot.item)
	return items

func move_item(from_index: int, to_index: int) -> bool:
	if from_index < 0 or from_index >= slots.size() or to_index < 0 or to_index >= slots.size():
		return false

	var temp_slot = slots[from_index]
	slots[from_index] = slots[to_index]
	slots[to_index] = temp_slot

	return true