{"weapons": [{"name": "Iron Sword", "description": "A sturdy iron blade", "type": "weapon", "rarity": "common", "equipment_slot": "weapon", "stat_modifiers": {"finesse": 3, "vitalis": 1}, "value": 50, "level_requirement": 1}, {"name": "<PERSON> Dagger", "description": "A dagger imbued with fire magic", "type": "weapon", "rarity": "rare", "equipment_slot": "weapon", "stat_modifiers": {"finesse": 5, "aether": 2}, "special_modifiers": ["Fire damage on critical hits"], "value": 200, "level_requirement": 5}, {"name": "Arcane Staff", "description": "A staff crackling with magical energy", "type": "weapon", "rarity": "epic", "equipment_slot": "weapon", "stat_modifiers": {"aether": 8, "vitalis": 2}, "special_modifiers": ["Increases spell damage by 15%"], "value": 500, "level_requirement": 10}], "armor": [{"name": "Leather Vest", "description": "Basic protection for adventurers", "type": "armor", "rarity": "common", "equipment_slot": "armor", "stat_modifiers": {"vitalis": 2, "resolve": 1}, "value": 30, "level_requirement": 1}, {"name": "<PERSON><PERSON>", "description": "Robes woven with protective enchantments", "type": "armor", "rarity": "uncommon", "equipment_slot": "armor", "stat_modifiers": {"aether": 4, "resolve": 2}, "value": 120, "level_requirement": 3}], "relics": [{"name": "Ring of Fortune", "description": "Increases the quality of found loot", "type": "relic", "rarity": "rare", "equipment_slot": "accessory", "stat_modifiers": {"craft": 5, "presence": 2}, "special_modifiers": ["10% chance for double loot drops"], "value": 300, "level_requirement": 5}, {"name": "Amulet of Vitality", "description": "Pulses with life-giving energy", "type": "relic", "rarity": "epic", "equipment_slot": "accessory", "stat_modifiers": {"vitalis": 6, "resolve": 3}, "special_modifiers": ["Regenerate 1 HP every 5 seconds"], "value": 400, "level_requirement": 8}], "consumables": [{"name": "Health Potion", "description": "Restores health when consumed", "type": "consumable", "rarity": "common", "consumable_effect": "heal", "effect_value": 50, "stack_size": 10, "value": 25}, {"name": "Greater Health Potion", "description": "Restores a large amount of health", "type": "consumable", "rarity": "uncommon", "consumable_effect": "heal", "effect_value": 150, "stack_size": 5, "value": 75}], "materials": [{"name": "Iron Ore", "description": "Raw iron ready for smelting", "type": "material", "rarity": "common", "stack_size": 50, "value": 5}, {"name": "Mystic Crystal", "description": "A crystal infused with magical energy", "type": "material", "rarity": "rare", "stack_size": 20, "value": 50}]}