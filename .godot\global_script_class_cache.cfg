list=[{
"base": &"Node",
"class": &"GameManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/GameManager.gd"
}, {
"base": &"Node",
"class": &"Inventory",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Inventory.gd"
}, {
"base": &"Resource",
"class": &"Item",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Item.gd"
}, {
"base": &"Node",
"class": &"LegacySystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/LegacySystem.gd"
}, {
"base": &"Node",
"class": &"LootSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/LootSystem.gd"
}, {
"base": &"CharacterBody2D",
"class": &"Player",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Player.gd"
}, {
"base": &"Node",
"class": &"ProjectValidator",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ProjectValidator.gd"
}, {
"base": &"Resource",
"class": &"Skill",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Skill.gd"
}, {
"base": &"Node",
"class": &"SkillManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/SkillManager.gd"
}, {
"base": &"Node",
"class": &"Stats",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Stats.gd"
}]
