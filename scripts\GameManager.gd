extends Node
class_name <PERSON>Manager

# System references
@onready var player: Player
@onready var stats: Stats
@onready var inventory: Inventory
@onready var skill_manager: <PERSON>llManager
@onready var loot_system: LootSystem
@onready var legacy_system: LegacySystem

# UI references (would be connected to actual UI nodes)
var ui_manager: UIManager

func _ready():
	setup_game_systems()
	setup_test_data()

func setup_game_systems():
	# Create system instances if they don't exist
	if not stats:
		stats = Stats.new()
		add_child(stats)
	
	if not inventory:
		inventory = Inventory.new()
		add_child(inventory)
	
	if not skill_manager:
		skill_manager = SkillManager.new()
		add_child(skill_manager)
	
	if not loot_system:
		loot_system = LootSystem.new()
		add_child(loot_system)
	
	if not legacy_system:
		legacy_system = LegacySystem.new()
		add_child(legacy_system)
	
	# Find player in scene
	player = get_node_or_null("Player")
	if not player:
		print("Warning: Player node not found in scene")
	
	# Setup system connections
	setup_system_connections()

func setup_system_connections():
	# Connect inventory to stats
	if inventory and stats:
		inventory.setup(stats)
	
	# Connect skill manager to player and stats
	if skill_manager and player and stats:
		skill_manager.setup(player, stats)
	
	# Connect loot system to stats
	if loot_system and stats:
		loot_system.setup(stats)
	
	# Connect legacy system to stats and player
	if legacy_system and stats and player:
		legacy_system.setup(stats, player)
	
	# Connect player movement speed to stats
	if player and stats:
		stats.connect("stat_changed", _on_stat_changed)
		# Update player speed based on finesse stat
		player.base_speed = stats.move_speed

func _on_stat_changed(stat_name: String, old_value: int, new_value: int):
	if stat_name == "finesse" and player:
		player.base_speed = stats.move_speed

func setup_test_data():
	# Load some test skills for demonstration
	load_test_skills()
	
	# Add some test items to inventory
	add_test_items()
	
	print("Game systems initialized successfully!")
	print("Player Stats - Level: " + str(stats.level) + ", HP: " + str(stats.current_health) + "/" + str(stats.max_health))
	print("Inventory slots: " + str(inventory.max_slots))
	print("Legacy system - Run: " + str(legacy_system.current_run))

func load_test_skills():
	# Load example skills
	var fireball_skill = load("res://resources/skills/fireball.tres") as Skill
	var heal_skill = load("res://resources/skills/heal.tres") as Skill
	var dash_strike_skill = load("res://resources/skills/dash_strike.tres") as Skill
	var vitality_boost_skill = load("res://resources/skills/vitality_boost.tres") as Skill
	
	if fireball_skill:
		skill_manager.set_active_skill("Q", fireball_skill)
		print("Loaded Fireball skill to Q")
	
	if heal_skill:
		skill_manager.set_active_skill("E", heal_skill)
		print("Loaded Heal skill to E")
	
	if dash_strike_skill:
		skill_manager.set_active_skill("R", dash_strike_skill)
		print("Loaded Dash Strike skill to R")
	
	if vitality_boost_skill:
		skill_manager.add_passive_skill(vitality_boost_skill)
		print("Added Vitality Boost passive skill")

func add_test_items():
	# Create some test items from the JSON data
	var test_sword = create_test_item("Iron Sword", Item.ItemType.WEAPON, Item.Rarity.COMMON)
	var test_potion = create_test_item("Health Potion", Item.ItemType.CONSUMABLE, Item.Rarity.COMMON)
	
	if test_sword:
		inventory.add_item(test_sword)
		print("Added Iron Sword to inventory")
	
	if test_potion:
		inventory.add_item(test_potion, 3)  # Add 3 potions
		print("Added 3 Health Potions to inventory")

func create_test_item(item_name: String, item_type: Item.ItemType, rarity: Item.Rarity) -> Item:
	var item = Item.new()
	item.item_name = item_name
	item.item_type = item_type
	item.rarity = rarity
	
	match item_name:
		"Iron Sword":
			item.description = "A sturdy iron blade"
			item.equipment_slot = "weapon"
			item.stat_modifiers = {"finesse": 3, "vitalis": 1}
			item.value = 50
		"Health Potion":
			item.description = "Restores health when consumed"
			item.consumable_effect = "heal"
			item.effect_value = 50
			item.stack_size = 10
			item.value = 25
	
	return item

# Input handling for testing
func _input(event):
	if event.is_action_pressed("ui_accept"):  # Enter key
		test_loot_generation()
	elif event.is_action_pressed("ui_select"):  # Space key (if not used for ultimate)
		test_inventory_operations()

func test_loot_generation():
	print("\n--- Testing Loot Generation ---")
	var generated_loot = loot_system.generate_loot(stats.level, 3)
	
	for item in generated_loot:
		print("Generated: " + item.get_rarity_name() + " " + item.item_name)
		inventory.add_item(item)

func test_inventory_operations():
	print("\n--- Testing Inventory Operations ---")
	print("Current inventory items:")
	
	for i in range(inventory.slots.size()):
		var slot = inventory.slots[i]
		if not slot.is_empty():
			print("Slot " + str(i) + ": " + slot.item.item_name + " x" + str(slot.quantity))

# Debug functions
func _on_player_level_up(new_level: int):
	print("Player leveled up to level " + str(new_level) + "!")

func _on_item_equipped(item: Item, slot: String):
	print("Equipped " + item.item_name + " in " + slot + " slot")

func _on_skill_activated(skill: Skill, target_position: Vector2):
	print("Activated skill: " + skill.skill_name + " at " + str(target_position))

func _on_loot_generated(items: Array[Item]):
	print("Generated " + str(items.size()) + " loot items")

func _on_rare_loot_found(item: Item):
	print("RARE LOOT FOUND: " + item.get_rarity_name() + " " + item.item_name + "!")

# Connect signals when systems are ready
func connect_debug_signals():
	if stats:
		stats.connect("level_up", _on_player_level_up)
	
	if inventory:
		inventory.connect("item_equipped", _on_item_equipped)
	
	if skill_manager:
		skill_manager.connect("skill_activated", _on_skill_activated)
	
	if loot_system:
		loot_system.connect("loot_generated", _on_loot_generated)
		loot_system.connect("rare_loot_found", _on_rare_loot_found)

func _notification(what):
	if what == NOTIFICATION_READY:
		# Connect debug signals after everything is ready
		call_deferred("connect_debug_signals")
