[gd_scene load_steps=4 format=3 uid="uid://cqxvqwgkqxqxq"]

[ext_resource type="Script" path="res://scripts/Player.gd" id="1_player"]
[ext_resource type="Texture2D" path="res://assets/character_knight.png" id="2_sprite"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(32, 32)

[node name="Player" type="CharacterBody2D"]
script = ExtResource("1_player")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_sprite")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]

[node name="DashCooldownTimer" type="Timer" parent="."]
wait_time = 1.0
one_shot = true