[gd_scene load_steps=3 format=3 uid="uid://bvxvqwgkqxqxq"]

[ext_resource type="Script" uid="uid://cjdb2eun830ru" path="res://scripts/GameManager.gd" id="1_0hdqr"]
[ext_resource type="PackedScene" uid="uid://cqxvqwgkqxqxq" path="res://scenes/Player.tscn" id="2_1mjkp"]

[node name="Main" type="Node2D"]
script = ExtResource("1_0hdqr")

[node name="Player" parent="." instance=ExtResource("2_1mjkp")]
position = Vector2(400, 300)

[node name="Camera2D" type="Camera2D" parent="Player"]

[node name="UI" type="CanvasLayer" parent="."]

[node name="DebugLabel" type="Label" parent="UI"]
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = -100.0
offset_right = 400.0
offset_bottom = -10.0
text = "WASD: Move | Shift: Dash | Q/E/R/F: Skills
Enter: Generate Loot | Space: Test Inventory
ESC: Quit"
vertical_alignment = 2
