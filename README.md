# Aetherborne - 2D Top-Down RPG

A comprehensive 2D top-down RPG built in Godot 4.4 with advanced systems including player movement, stats, skills, inventory, permadeath with legacy traits, and procedural loot generation.

## 🎮 Features Implemented

### ✅ Core Systems
- **Player Movement**: WASD movement with dash mechanics (Shift key)
- **RPG Stat System**: 6 core stats affecting gameplay (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>er, Resolve, Craft, Presence)
- **Skill System**: 3 active skills (Q/E/R), 2 passives, 1 ultimate (F)
- **Inventory System**: Drag-and-drop ready with equipment and consumables
- **Permadeath & Legacy**: Choose traits to pass between runs
- **Loot Generation**: Weighted probability system with rarity scaling

### 🚧 Systems To Be Implemented
- Procedural Dungeon Generator
- Crafting System
- Ultimate Skill System (Totem Surge)
- Minimap with Fog of War

## 📁 Project Structure

```
aetherbornetest/
├── project.godot              # Main project file
├── README.md                  # This file
├── assets/                    # Game assets
│   ├── character_knight.png   # Player sprite
│   ├── character_shardborn.png
│   ├── dagger.png
│   ├── relic.png
│   ├── staff.png
│   ├── sword.png
│   └── tileset_ruins.png
├── data/                      # Game data files
│   ├── items.json            # Item database
│   └── traits.json           # Legacy traits
├── resources/                 # Godot resources
│   └── skills/               # Skill definitions
│       ├── fireball.tres
│       ├── heal.tres
│       ├── dash_strike.tres
│       └── vitality_boost.tres
├── scenes/                    # Scene files
│   ├── MainScene.tscn        # Main game scene
│   ├── Player.tscn           # Player character
│   ├── CraftingUI.tscn       # (Placeholder)
│   ├── EnemyDummy.tscn       # (Placeholder)
│   ├── LegacyPanel.tscn      # (Placeholder)
│   └── LootPickup.tscn       # (Placeholder)
├── scripts/                   # GDScript files
│   ├── Player.gd             # Player movement & dash
│   ├── Stats.gd              # RPG stat system
│   ├── SkillManager.gd       # Skill handling
│   ├── Skill.gd              # Skill resource class
│   ├── Inventory.gd          # Inventory management
│   ├── Item.gd               # Item resource class
│   ├── LegacySystem.gd       # Permadeath & traits
│   ├── LootSystem.gd         # Loot generation
│   ├── GameManager.gd        # System integration
│   ├── SaveLoad.gd           # (Placeholder)
│   └── UIManager.gd          # (Placeholder)
└── ui/                        # UI assets
    ├── icon_aether.png
    ├── icon_hp.png
    ├── icon_xp.png
    └── inventory_slot.png
```

## 🚀 Setup Instructions

### Prerequisites
- **Godot Engine 4.4** or later
- Basic understanding of Godot project structure

### Step 1: Import Project
1. Open Godot Engine
2. Click "Import" on the project manager
3. Navigate to the project folder and select `project.godot`
4. Click "Import & Edit"

### Step 2: Verify Project Settings
The project should automatically configure, but verify these settings:

**Project Settings → Input Map:**
- `move_up` → W key
- `move_down` → S key
- `move_left` → A key
- `move_right` → D key
- `dash` → Shift key
- `skill_q` → Q key
- `skill_e` → E key
- `skill_r` → R key
- `skill_f` → F key
- `ultimate` → Spacebar
- `toggle_map` → M key

**Project Settings → Application:**
- Main Scene: `res://scenes/MainScene.tscn`

### Step 3: Check Asset Imports
Ensure all assets in the `assets/` folder are properly imported:
1. Go to FileSystem dock
2. Check that `.png` files have corresponding `.import` files
3. If any assets show import errors, select them and reimport

### Step 4: Verify Scene Structure
**MainScene.tscn should contain:**
- Main (Node2D) with GameManager.gd script
- Player (CharacterBody2D instance from Player.tscn)
- Camera2D (child of Player)
- UI (CanvasLayer) with debug labels

**Player.tscn should contain:**
- Player (CharacterBody2D) with Player.gd script
- Sprite2D with character texture
- CollisionShape2D with RectangleShape2D
- AnimationPlayer (for future animations)
- DashCooldownTimer

### Step 5: Test the Game
1. Press F5 or click the Play button
2. Select MainScene.tscn as the main scene if prompted
3. Test controls:
   - **WASD**: Move player
   - **Shift**: Dash (with cooldown)
   - **Q/E/R**: Cast skills (Fireball/Heal/Dash Strike)
   - **F**: Ultimate skill slot
   - **Enter**: Generate test loot
   - **Space**: Display inventory contents

## 🎯 Controls

| Key | Action |
|-----|--------|
| W/A/S/D | Move player |
| Shift | Dash (1 second cooldown) |
| Q | Fireball skill |
| E | Heal skill |
| R | Dash Strike skill |
| F | Ultimate skill (placeholder) |
| Spacebar | Totem Surge (when implemented) |
| M | Toggle minimap (when implemented) |
| Enter | Generate test loot |
| Space | Show inventory debug info |

## 🔧 System Overview

### Stats System
- **Vitalis**: Health points (10 HP per point + 5 per level)
- **Finesse**: Movement speed (5 units per point) & crit chance (2% per point)
- **Aether**: Magic power (3 per point + level) & mana capacity
- **Resolve**: Damage resistance (2 per point)
- **Craft**: Loot quality bonus (5% per point)
- **Presence**: NPC interaction bonus (10% per point)

### Skill System
- **Active Skills**: Q/E/R slots with cooldowns and resource costs
- **Passive Skills**: Permanent stat bonuses
- **Ultimate Skills**: F slot for powerful abilities
- **Skill Resources**: Defined in `.tres` files with damage, cooldown, costs

### Inventory System
- **30 slots** by default (configurable)
- **Item Types**: Weapon, Armor, Relic, Consumable, Material
- **Rarity Levels**: Common → Uncommon → Rare → Epic → Legendary → Mythic
- **Equipment Slots**: Weapon, Armor, Accessory

### Legacy System
- **12 different traits** available
- **Choose 1-2 traits** per death to pass to next run
- **Persistent storage** in user://legacy_data.json
- **Run statistics** and history tracking

## 🐛 Troubleshooting

### Common Issues

**"Script class not found" errors:**
- Ensure all `.gd` files are in the `scripts/` folder
- Check that class names match: `class_name Player`, `class_name Stats`, etc.

**"Resource not found" errors:**
- Verify `.tres` files exist in `resources/skills/`
- Check file paths in GameManager.gd load statements

**Input not working:**
- Verify Input Map in Project Settings
- Ensure action names match those used in scripts

**Assets not displaying:**
- Check that `.png.import` files exist alongside assets
- Reimport assets if necessary (right-click → Reimport)

### Debug Information
The game prints debug information to the console:
- System initialization status
- Skill activations
- Loot generation results
- Inventory operations
- Legacy trait applications

## 📝 Development Notes

### Extending the System
- **Add new skills**: Create `.tres` files in `resources/skills/`
- **Add new items**: Edit `data/items.json`
- **Add new traits**: Edit `data/traits.json`
- **Modify stats**: Adjust calculations in `Stats.gd`

### Performance Considerations
- Systems are designed to be modular and efficient
- Loot generation uses weighted probability for performance
- Inventory uses slot-based system for O(1) operations where possible

## 🎨 Art Requirements
Current placeholder assets should be replaced with:
- **32x32 pixel** character sprites
- **Weapon/item icons** for inventory
- **UI elements** for stat panels and inventory
- **Tileset** for dungeon generation (when implemented)

---

**Ready to play!** The core systems are functional and ready for testing. The remaining systems (dungeon generation, crafting, ultimate skills, minimap) can be implemented as needed.
