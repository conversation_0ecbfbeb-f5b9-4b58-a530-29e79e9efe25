extends Node
class_name ProjectValidator

# Simple validation script to check if all systems are properly set up
# Run this in the editor or attach to a node to validate the project

func _ready():
	validate_project()

func validate_project():
	print("=== Aetherborne Project Validation ===")
	
	var all_valid = true
	
	# Check scripts
	all_valid = validate_scripts() and all_valid
	
	# Check resources
	all_valid = validate_resources() and all_valid
	
	# Check data files
	all_valid = validate_data_files() and all_valid
	
	# Check scenes
	all_valid = validate_scenes() and all_valid
	
	if all_valid:
		print("✅ Project validation PASSED - Ready for Godot import!")
	else:
		print("❌ Project validation FAILED - Check errors above")
	
	print("=====================================")

func validate_scripts() -> bool:
	print("\n📜 Validating Scripts...")
	var valid = true
	
	var required_scripts = [
		"res://scripts/Player.gd",
		"res://scripts/Stats.gd",
		"res://scripts/SkillManager.gd",
		"res://scripts/Skill.gd",
		"res://scripts/Inventory.gd",
		"res://scripts/Item.gd",
		"res://scripts/LegacySystem.gd",
		"res://scripts/LootSystem.gd",
		"res://scripts/GameManager.gd"
	]
	
	for script_path in required_scripts:
		if FileAccess.file_exists(script_path):
			print("  ✅ " + script_path)
		else:
			print("  ❌ MISSING: " + script_path)
			valid = false
	
	return valid

func validate_resources() -> bool:
	print("\n🎯 Validating Skill Resources...")
	var valid = true
	
	var required_skills = [
		"res://resources/skills/fireball.tres",
		"res://resources/skills/heal.tres",
		"res://resources/skills/dash_strike.tres",
		"res://resources/skills/vitality_boost.tres"
	]
	
	for skill_path in required_skills:
		if FileAccess.file_exists(skill_path):
			print("  ✅ " + skill_path)
		else:
			print("  ❌ MISSING: " + skill_path)
			valid = false
	
	return valid

func validate_data_files() -> bool:
	print("\n📊 Validating Data Files...")
	var valid = true
	
	# Check items.json
	if FileAccess.file_exists("res://data/items.json"):
		print("  ✅ items.json")
		var file = FileAccess.open("res://data/items.json", FileAccess.READ)
		if file:
			var content = file.get_as_text()
			file.close()
			var json = JSON.new()
			if json.parse(content) == OK:
				print("    ✅ Valid JSON format")
			else:
				print("    ❌ Invalid JSON format")
				valid = false
	else:
		print("  ❌ MISSING: items.json")
		valid = false
	
	# Check traits.json
	if FileAccess.file_exists("res://data/traits.json"):
		print("  ✅ traits.json")
		var file = FileAccess.open("res://data/traits.json", FileAccess.READ)
		if file:
			var content = file.get_as_text()
			file.close()
			var json = JSON.new()
			if json.parse(content) == OK:
				print("    ✅ Valid JSON format")
			else:
				print("    ❌ Invalid JSON format")
				valid = false
	else:
		print("  ❌ MISSING: traits.json")
		valid = false
	
	return valid

func validate_scenes() -> bool:
	print("\n🎬 Validating Scenes...")
	var valid = true
	
	if FileAccess.file_exists("res://scenes/MainScene.tscn"):
		print("  ✅ MainScene.tscn")
	else:
		print("  ❌ MISSING: MainScene.tscn")
		valid = false
	
	if FileAccess.file_exists("res://scenes/Player.tscn"):
		print("  ✅ Player.tscn")
	else:
		print("  ❌ MISSING: Player.tscn")
		valid = false
	
	return valid

# Function to test system integration
func test_system_integration():
	print("\n🔧 Testing System Integration...")
	
	# Test creating instances
	var stats = Stats.new()
	var inventory = Inventory.new()
	var skill_manager = SkillManager.new()
	var loot_system = LootSystem.new()
	var legacy_system = LegacySystem.new()
	
	print("  ✅ All system classes can be instantiated")
	
	# Test basic functionality
	stats.add_experience(50)
	print("  ✅ Stats system functional")
	
	var test_item = Item.new()
	test_item.item_name = "Test Item"
	inventory.add_item(test_item)
	print("  ✅ Inventory system functional")
	
	print("  ✅ System integration test passed")

# Call this function manually to run integration tests
func run_integration_test():
	validate_project()
	test_system_integration()
