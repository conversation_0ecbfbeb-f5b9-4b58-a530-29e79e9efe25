extends Node
class_name LootSystem

# Loot generation signals
signal loot_generated(items: Array[Item])
signal rare_loot_found(item: Item)

# Loot tables and probabilities
var base_rarity_weights = {
	Item.Rarity.COMMON: 50.0,
	Item.Rarity.UNCOMMON: 25.0,
	Item.Rarity.RARE: 15.0,
	Item.Rarity.EPIC: 7.0,
	Item.Rarity.LEGENDARY: 2.5,
	Item.Rarity.MYTHIC: 0.5
}

var item_type_weights = {
	Item.ItemType.WEAPON: 20.0,
	Item.ItemType.ARMOR: 20.0,
	Item.ItemType.RELIC: 10.0,
	Item.ItemType.CONSUMABLE: 35.0,
	Item.ItemType.MATERIAL: 15.0
}

# Item database loaded from JSON
var item_database: Dictionary = {}

# References
var stats: Stats

func _ready():
	load_item_database()

func setup(stats_ref: Stats):
	stats = stats_ref

func load_item_database():
	var file = FileAccess.open("res://data/items.json", FileAccess.READ)
	if file:
		var json_text = file.get_as_text()
		file.close()

		var json = JSON.new()
		var parse_result = json.parse(json_text)

		if parse_result == OK:
			item_database = json.data
			print("Item database loaded with " + str(get_total_items()) + " items")

func get_total_items() -> int:
	var total = 0
	for category in item_database.values():
		if category is Array:
			total += category.size()
	return total

func generate_loot(enemy_level: int, loot_count: int = 1, bonus_rarity_chance: float = 0.0) -> Array[Item]:
	var generated_items: Array[Item] = []

	for i in range(loot_count):
		var item = generate_single_item(enemy_level, bonus_rarity_chance)
		if item:
			generated_items.append(item)

	if generated_items.size() > 0:
		loot_generated.emit(generated_items)

	return generated_items

func generate_single_item(enemy_level: int, bonus_rarity_chance: float = 0.0) -> Item:
	# Determine item type
	var item_type = select_weighted_item_type()

	# Determine rarity based on enemy level, player craft stat, and bonus chance
	var rarity = select_item_rarity(enemy_level, bonus_rarity_chance)

	# Get items of the selected type and rarity
	var available_items = get_items_by_type_and_rarity(item_type, rarity)

	if available_items.is_empty():
		# Fallback to any item of the selected rarity
		available_items = get_items_by_rarity(rarity)

	if available_items.is_empty():
		# Final fallback to common items
		available_items = get_items_by_rarity(Item.Rarity.COMMON)

	if available_items.is_empty():
		print("No items available for generation!")
		return null

	# Select random item from available options
	var selected_item_data = available_items[randi() % available_items.size()]

	# Create item instance
	var item = create_item_from_data(selected_item_data)

	# Apply level scaling and random modifiers
	apply_level_scaling(item, enemy_level)
	apply_random_modifiers(item, rarity)

	# Check if it's a rare find
	if rarity >= Item.Rarity.EPIC:
		rare_loot_found.emit(item)

	return item

func select_weighted_item_type() -> Item.ItemType:
	var total_weight = 0.0
	for weight in item_type_weights.values():
		total_weight += weight

	var random_value = randf() * total_weight
	var current_weight = 0.0

	for item_type in item_type_weights:
		current_weight += item_type_weights[item_type]
		if random_value <= current_weight:
			return item_type

	return Item.ItemType.CONSUMABLE  # Fallback

func select_item_rarity(enemy_level: int, bonus_chance: float = 0.0) -> Item.Rarity:
	# Calculate modified weights based on enemy level and player craft stat
	var modified_weights = base_rarity_weights.duplicate()

	# Enemy level bonus (higher level = better loot)
	var level_bonus = enemy_level * 0.5
	modified_weights[Item.Rarity.UNCOMMON] += level_bonus * 0.5
	modified_weights[Item.Rarity.RARE] += level_bonus * 0.3
	modified_weights[Item.Rarity.EPIC] += level_bonus * 0.15
	modified_weights[Item.Rarity.LEGENDARY] += level_bonus * 0.05

	# Player craft stat bonus
	if stats:
		var craft_bonus = stats.craft * 0.2
		modified_weights[Item.Rarity.RARE] += craft_bonus
		modified_weights[Item.Rarity.EPIC] += craft_bonus * 0.5
		modified_weights[Item.Rarity.LEGENDARY] += craft_bonus * 0.25
		modified_weights[Item.Rarity.MYTHIC] += craft_bonus * 0.1

	# Apply bonus rarity chance
	if bonus_chance > 0:
		modified_weights[Item.Rarity.RARE] += bonus_chance * 10
		modified_weights[Item.Rarity.EPIC] += bonus_chance * 5
		modified_weights[Item.Rarity.LEGENDARY] += bonus_chance * 2
		modified_weights[Item.Rarity.MYTHIC] += bonus_chance

	# Select based on weighted probability
	var total_weight = 0.0
	for weight in modified_weights.values():
		total_weight += weight

	var random_value = randf() * total_weight
	var current_weight = 0.0

	for rarity in modified_weights:
		current_weight += modified_weights[rarity]
		if random_value <= current_weight:
			return rarity

	return Item.Rarity.COMMON  # Fallback

func get_items_by_type_and_rarity(item_type: Item.ItemType, rarity: Item.Rarity) -> Array:
	var matching_items = []
	var type_name = get_item_type_category_name(item_type)

	if type_name in item_database:
		for item_data in item_database[type_name]:
			if get_rarity_from_string(item_data.get("rarity", "common")) == rarity:
				matching_items.append(item_data)

	return matching_items

func get_items_by_rarity(rarity: Item.Rarity) -> Array:
	var matching_items = []

	for category in item_database.values():
		if category is Array:
			for item_data in category:
				if get_rarity_from_string(item_data.get("rarity", "common")) == rarity:
					matching_items.append(item_data)

	return matching_items

func get_item_type_category_name(item_type: Item.ItemType) -> String:
	match item_type:
		Item.ItemType.WEAPON:
			return "weapons"
		Item.ItemType.ARMOR:
			return "armor"
		Item.ItemType.RELIC:
			return "relics"
		Item.ItemType.CONSUMABLE:
			return "consumables"
		Item.ItemType.MATERIAL:
			return "materials"
		_:
			return "weapons"

func get_rarity_from_string(rarity_string: String) -> Item.Rarity:
	match rarity_string.to_lower():
		"common":
			return Item.Rarity.COMMON
		"uncommon":
			return Item.Rarity.UNCOMMON
		"rare":
			return Item.Rarity.RARE
		"epic":
			return Item.Rarity.EPIC
		"legendary":
			return Item.Rarity.LEGENDARY
		"mythic":
			return Item.Rarity.MYTHIC
		_:
			return Item.Rarity.COMMON

func get_item_type_from_string(type_string: String) -> Item.ItemType:
	match type_string.to_lower():
		"weapon":
			return Item.ItemType.WEAPON
		"armor":
			return Item.ItemType.ARMOR
		"relic":
			return Item.ItemType.RELIC
		"consumable":
			return Item.ItemType.CONSUMABLE
		"material":
			return Item.ItemType.MATERIAL
		_:
			return Item.ItemType.WEAPON

func create_item_from_data(item_data: Dictionary) -> Item:
	var item = Item.new()

	item.item_name = item_data.get("name", "Unknown Item")
	item.description = item_data.get("description", "")
	item.item_type = get_item_type_from_string(item_data.get("type", "weapon"))
	item.rarity = get_rarity_from_string(item_data.get("rarity", "common"))
	item.value = item_data.get("value", 1)
	item.equipment_slot = item_data.get("equipment_slot", "")
	item.level_requirement = item_data.get("level_requirement", 1)
	item.consumable_effect = item_data.get("consumable_effect", "")
	item.effect_value = item_data.get("effect_value", 0)
	item.stack_size = item_data.get("stack_size", 1)

	# Copy stat modifiers
	if item_data.has("stat_modifiers"):
		item.stat_modifiers = item_data["stat_modifiers"].duplicate()

	# Copy special modifiers
	if item_data.has("special_modifiers"):
		item.special_modifiers = item_data["special_modifiers"].duplicate()

	return item

func apply_level_scaling(item: Item, enemy_level: int):
	# Scale item stats based on enemy level
	var level_multiplier = 1.0 + (enemy_level - 1) * 0.1  # 10% increase per level

	for stat_name in item.stat_modifiers:
		var original_value = item.stat_modifiers[stat_name]
		item.stat_modifiers[stat_name] = int(original_value * level_multiplier)

	# Scale value
	item.value = int(item.value * level_multiplier)

func apply_random_modifiers(item: Item, rarity: Item.Rarity):
	# Higher rarity items have a chance for additional random modifiers
	var modifier_chance = 0.0

	match rarity:
		Item.Rarity.UNCOMMON:
			modifier_chance = 0.2
		Item.Rarity.RARE:
			modifier_chance = 0.4
		Item.Rarity.EPIC:
			modifier_chance = 0.6
		Item.Rarity.LEGENDARY:
			modifier_chance = 0.8
		Item.Rarity.MYTHIC:
			modifier_chance = 1.0

	if randf() < modifier_chance:
		add_random_modifier(item)

func add_random_modifier(item: Item):
	var possible_stats = ["vitalis", "finesse", "aether", "resolve", "craft", "presence"]
	var random_stat = possible_stats[randi() % possible_stats.size()]
	var bonus_value = randi_range(1, 3)

	if random_stat in item.stat_modifiers:
		item.stat_modifiers[random_stat] += bonus_value
	else:
		item.stat_modifiers[random_stat] = bonus_value

	# Update item name to indicate it has random modifiers
	if not item.item_name.begins_with("Enhanced "):
		item.item_name = "Enhanced " + item.item_name

	# Increase value
	item.value = int(item.value * 1.2)

# Utility functions for external systems
func generate_chest_loot(chest_level: int) -> Array[Item]:
	var loot_count = randi_range(1, 3)  # 1-3 items per chest
	var bonus_chance = 0.1  # 10% bonus for rare loot from chests
	return generate_loot(chest_level, loot_count, bonus_chance)

func generate_boss_loot(boss_level: int) -> Array[Item]:
	var loot_count = randi_range(2, 4)  # 2-4 items from bosses
	var bonus_chance = 0.3  # 30% bonus for rare loot from bosses
	return generate_loot(boss_level, loot_count, bonus_chance)

func generate_material_drop(enemy_level: int) -> Item:
	# Force material type
	var materials = get_items_by_type_and_rarity(Item.ItemType.MATERIAL, Item.Rarity.COMMON)
	if materials.is_empty():
		return null

	var material_data = materials[randi() % materials.size()]
	var item = create_item_from_data(material_data)
	apply_level_scaling(item, enemy_level)

	return item