extends Resource
class_name Skill

enum SkillType {
	ACTIVE,
	PASSIVE,
	ULTIMATE
}

enum TargetType {
	SELF,
	ENEMY,
	AREA,
	DIRECTION
}

@export var skill_name: String = ""
@export var description: String = ""
@export var skill_type: SkillType = SkillType.ACTIVE
@export var target_type: TargetType = TargetType.ENEMY

# Damage and effects
@export var base_damage: int = 0
@export var scaling_stat: String = "aether"  # Which stat scales the damage
@export var scaling_ratio: float = 1.0  # How much the stat affects damage

# Resource costs
@export var aether_cost: int = 0
@export var health_cost: int = 0

# Timing
@export var cooldown: float = 1.0
@export var cast_time: float = 0.0
@export var duration: float = 0.0  # For buffs/debuffs

# Range and area
@export var range: float = 100.0
@export var area_radius: float = 0.0

# Visual effects
@export var cast_animation: String = ""
@export var effect_scene: PackedScene
@export var sound_effect: AudioStream

# Passive effects (for passive skills)
@export var stat_modifiers: Dictionary = {}
@export var special_effects: Array[String] = []

# Level scaling
@export var max_level: int = 5
@export var level_damage_bonus: int = 10  # Damage increase per level
@export var level_cooldown_reduction: float = 0.1  # Cooldown reduction per level

func get_actual_damage(caster_stats: Stats, skill_level: int = 1) -> int:
	var base = base_damage + (level_damage_bonus * (skill_level - 1))
	var stat_value = caster_stats.get_stat_value(scaling_stat)
	return int(base + (stat_value * scaling_ratio))

func get_actual_cooldown(skill_level: int = 1) -> float:
	return max(0.1, cooldown - (level_cooldown_reduction * (skill_level - 1)))

func can_cast(caster_stats: Stats) -> bool:
	if aether_cost > 0 and caster_stats.aether < aether_cost:
		return false
	if health_cost > 0 and caster_stats.current_health <= health_cost:
		return false
	return true

func get_tooltip_text(skill_level: int = 1) -> String:
	var tooltip = skill_name + "\n"
	tooltip += description + "\n\n"
	
	if skill_type == SkillType.ACTIVE:
		if base_damage > 0:
			tooltip += "Damage: " + str(get_actual_damage(null, skill_level)) + "\n"
		if aether_cost > 0:
			tooltip += "Aether Cost: " + str(aether_cost) + "\n"
		if health_cost > 0:
			tooltip += "Health Cost: " + str(health_cost) + "\n"
		tooltip += "Cooldown: " + str(get_actual_cooldown(skill_level)) + "s\n"
		if cast_time > 0:
			tooltip += "Cast Time: " + str(cast_time) + "s\n"
	elif skill_type == SkillType.PASSIVE:
		tooltip += "Passive Effect\n"
		for stat in stat_modifiers:
			tooltip += stat.capitalize() + ": +" + str(stat_modifiers[stat]) + "\n"
	
	return tooltip
