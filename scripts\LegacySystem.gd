extends Node
class_name LegacySystem

# Legacy system signals
signal player_died()
signal trait_selected(trait_id: String)
signal new_run_started(run_number: int)
signal legacy_loaded()

# Trait structure
class LegacyTrait:
	var id: String
	var name: String
	var description: String
	var type: String
	var stat_modifiers: Dictionary
	var special_effects: Array[String]
	
	func _init(data: Dictionary):
		id = data.get("id", "")
		name = data.get("name", "")
		description = data.get("description", "")
		type = data.get("type", "")
		stat_modifiers = data.get("stat_modifiers", {})
		special_effects = data.get("special_effects", [])

# Legacy data
var available_traits: Array[LegacyTrait] = []
var unlocked_traits: Array[String] = []
var active_traits: Array[String] = []
var current_run: int = 1
var total_deaths: int = 0
var run_history: Array[Dictionary] = []

# File paths
const TRAITS_FILE_PATH = "user://legacy_data.json"
const DEFAULT_TRAITS_PATH = "res://data/traits.json"

# References
var stats: Stats
var player: Player

func _ready():
	load_legacy_data()

func setup(stats_ref: Stats, player_ref: Player):
	stats = stats_ref
	player = player_ref
	
	# Connect to player death
	if stats:
		stats.connect("health_changed", _on_health_changed)
	
	# Apply active legacy traits
	apply_active_traits()

func _on_health_changed(current_hp: int, max_hp: int):
	if current_hp <= 0:
		handle_player_death()

func handle_player_death():
	print("Player has died! Activating legacy system...")
	total_deaths += 1
	
	# Record this run
	record_current_run()
	
	# Emit death signal
	player_died.emit()
	
	# Show legacy selection UI (would be handled by UI system)
	show_legacy_selection()

func record_current_run():
	var run_data = {
		"run_number": current_run,
		"level_reached": stats.level if stats else 1,
		"experience_gained": stats.experience if stats else 0,
		"traits_used": active_traits.duplicate(),
		"death_timestamp": Time.get_unix_time_from_system()
	}
	
	run_history.append(run_data)

func show_legacy_selection():
	# This would typically show a UI panel
	# For now, we'll just print available options
	print("Choose up to 2 legacy traits for your next run:")
	
	var selectable_traits = get_selectable_traits()
	for i in range(selectable_traits.size()):
		var trait = selectable_traits[i]
		print(str(i + 1) + ". " + trait.name + " - " + trait.description)

func get_selectable_traits() -> Array[LegacyTrait]:
	var selectable: Array[LegacyTrait] = []
	
	for trait in available_traits:
		if trait.id in unlocked_traits:
			selectable.append(trait)
	
	return selectable

func select_legacy_trait(trait_id: String) -> bool:
	if active_traits.size() >= 2:
		print("Cannot select more than 2 legacy traits")
		return false
	
	if trait_id in active_traits:
		print("Trait already selected")
		return false
	
	if not trait_id in unlocked_traits:
		print("Trait not unlocked")
		return false
	
	active_traits.append(trait_id)
	trait_selected.emit(trait_id)
	print("Selected legacy trait: " + get_trait_by_id(trait_id).name)
	
	return true

func remove_legacy_trait(trait_id: String) -> bool:
	if trait_id in active_traits:
		active_traits.erase(trait_id)
		return true
	return false

func start_new_run():
	current_run += 1
	
	# Save legacy data
	save_legacy_data()
	
	# Apply active traits to new character
	apply_active_traits()
	
	new_run_started.emit(current_run)
	print("Starting run #" + str(current_run) + " with " + str(active_traits.size()) + " legacy traits")

func apply_active_traits():
	if not stats:
		return
	
	for trait_id in active_traits:
		var trait = get_trait_by_id(trait_id)
		if trait:
			apply_trait_effects(trait)

func apply_trait_effects(trait: LegacyTrait):
	# Apply stat modifiers
	for stat_name in trait.stat_modifiers:
		var value = trait.stat_modifiers[stat_name]
		stats.add_stat_modifier(stat_name, value)
	
	# Apply special effects
	for effect in trait.special_effects:
		apply_special_effect(effect)

func apply_special_effect(effect: String):
	match effect:
		"physical_damage_bonus_5":
			# Would implement damage bonus
			print("Applied 5% physical damage bonus")
		"magic_damage_bonus_5":
			print("Applied 5% magic damage bonus")
		"crafting_xp_bonus_10":
			print("Applied 10% crafting XP bonus")
		"loot_quality_bonus":
			print("Applied loot quality bonus")
		"max_health_bonus_15":
			if stats:
				var bonus_health = int(stats.max_health * 0.15)
				stats.add_stat_modifier("vitalis", bonus_health / 10)  # Convert to vitalis
		"movement_speed_bonus_10":
			if player:
				player.base_speed *= 1.1
		"dash_cooldown_reduction":
			if player:
				player.dash_cooldown *= 0.9
		"skill_cooldown_reduction_20":
			print("Applied 20% skill cooldown reduction")
		"damage_resistance_25":
			print("Applied 25% damage resistance")
		"shop_discount_15":
			print("Applied 15% shop discount")
		"rare_loot_chance_25":
			print("Applied 25% rare loot chance bonus")
		"crit_chance_bonus_10":
			print("Applied 10% critical hit chance bonus")
		"extra_skill_point":
			print("Granted extra skill point")
		"xp_bonus_15":
			print("Applied 15% XP bonus")
		_:
			print("Unknown special effect: " + effect)

func get_trait_by_id(trait_id: String) -> LegacyTrait:
	for trait in available_traits:
		if trait.id == trait_id:
			return trait
	return null

func unlock_trait(trait_id: String):
	if not trait_id in unlocked_traits:
		unlocked_traits.append(trait_id)
		print("Unlocked new legacy trait: " + get_trait_by_id(trait_id).name)

func load_legacy_data():
	# First load default traits
	load_default_traits()
	
	# Then load user legacy data if it exists
	if FileAccess.file_exists(TRAITS_FILE_PATH):
		var file = FileAccess.open(TRAITS_FILE_PATH, FileAccess.READ)
		if file:
			var json_text = file.get_as_text()
			file.close()
			
			var json = JSON.new()
			var parse_result = json.parse(json_text)
			
			if parse_result == OK:
				var data = json.data
				if data.has("legacy_data"):
					var legacy_data = data["legacy_data"]
					current_run = legacy_data.get("current_run", 1)
					total_deaths = legacy_data.get("total_deaths", 0)
					active_traits = legacy_data.get("active_traits", [])
					unlocked_traits = legacy_data.get("unlocked_traits", [])
					run_history = legacy_data.get("run_history", [])
	
	legacy_loaded.emit()

func load_default_traits():
	var file = FileAccess.open(DEFAULT_TRAITS_PATH, FileAccess.READ)
	if file:
		var json_text = file.get_as_text()
		file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(json_text)
		
		if parse_result == OK:
			var data = json.data
			if data.has("available_traits"):
				available_traits.clear()
				for trait_data in data["available_traits"]:
					available_traits.append(LegacyTrait.new(trait_data))
			
			# Load initial unlocked traits if this is first time
			if unlocked_traits.is_empty() and data.has("legacy_data"):
				unlocked_traits = data["legacy_data"].get("unlocked_traits", [])

func save_legacy_data():
	var save_data = {
		"legacy_data": {
			"current_run": current_run,
			"total_deaths": total_deaths,
			"active_traits": active_traits,
			"unlocked_traits": unlocked_traits,
			"run_history": run_history
		}
	}
	
	var file = FileAccess.open(TRAITS_FILE_PATH, FileAccess.WRITE)
	if file:
		var json_string = JSON.stringify(save_data, "\t")
		file.store_string(json_string)
		file.close()
		print("Legacy data saved")

func get_run_statistics() -> Dictionary:
	return {
		"current_run": current_run,
		"total_deaths": total_deaths,
		"active_traits_count": active_traits.size(),
		"unlocked_traits_count": unlocked_traits.size(),
		"total_runs": run_history.size()
	}

func reset_legacy_data():
	current_run = 1
	total_deaths = 0
	active_traits.clear()
	run_history.clear()
	# Keep unlocked traits but could reset those too if desired
	save_legacy_data()
	print("Legacy data reset")
