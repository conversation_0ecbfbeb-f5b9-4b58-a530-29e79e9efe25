extends Resource
class_name Item

enum ItemType {
	WEA<PERSON><PERSON>,
	<PERSON>MO<PERSON>,
	<PERSON><PERSON><PERSON>,
	CO<PERSON><PERSON><PERSON><PERSON>,
	MATERIAL
}

enum Rarity {
	COMMON,
	UNCOMMON,
	RARE,
	EPIC,
	LEGENDARY,
	MYTHIC
}

@export var item_name: String = ""
@export var description: String = ""
@export var item_type: ItemType = ItemType.WEAPON
@export var rarity: Rarity = Rarity.COMMON
@export var icon: Texture2D
@export var value: int = 1

# Stats and modifiers
@export var stat_modifiers: Dictionary = {}
@export var special_modifiers: Array[String] = []

# Equipment specific
@export var equipment_slot: String = ""  # "weapon", "armor", "accessory"
@export var level_requirement: int = 1

# Consumable specific
@export var consumable_effect: String = ""
@export var effect_value: int = 0
@export var stack_size: int = 1

# Crafting
@export var is_craftable: bool = false
@export var crafting_materials: Dictionary = {}

func get_rarity_color() -> Color:
	match rarity:
		Rarity.COMMON:
			return Color.WHITE
		Rarity.UNCOMMON:
			return Color.GREEN
		Rarity.RARE:
			return Color.BLUE
		Rarity.EPIC:
			return Color.PURPLE
		Rarity.LEGENDARY:
			return Color.ORANGE
		Rarity.MYTHIC:
			return Color.RED
		_:
			return Color.WHITE

func get_rarity_name() -> String:
	match rarity:
		Rarity.COMMON:
			return "Common"
		Rarity.UNCOMMON:
			return "Uncommon"
		Rarity.RARE:
			return "Rare"
		Rarity.EPIC:
			return "Epic"
		Rarity.LEGENDARY:
			return "Legendary"
		Rarity.MYTHIC:
			return "Mythic"
		_:
			return "Unknown"

func get_type_name() -> String:
	match item_type:
		ItemType.WEAPON:
			return "Weapon"
		ItemType.ARMOR:
			return "Armor"
		ItemType.RELIC:
			return "Relic"
		ItemType.CONSUMABLE:
			return "Consumable"
		ItemType.MATERIAL:
			return "Material"
		_:
			return "Unknown"

func get_tooltip_text() -> String:
	var tooltip = "[color=" + get_rarity_color().to_html() + "]" + item_name + "[/color]\n"
	tooltip += get_rarity_name() + " " + get_type_name() + "\n\n"
	tooltip += description + "\n"
	
	if stat_modifiers.size() > 0:
		tooltip += "\nStats:\n"
		for stat in stat_modifiers:
			var value = stat_modifiers[stat]
			var prefix = "+" if value > 0 else ""
			tooltip += prefix + str(value) + " " + stat.capitalize() + "\n"
	
	if special_modifiers.size() > 0:
		tooltip += "\nSpecial Effects:\n"
		for modifier in special_modifiers:
			tooltip += "• " + modifier + "\n"
	
	if level_requirement > 1:
		tooltip += "\nLevel Requirement: " + str(level_requirement)
	
	tooltip += "\nValue: " + str(value) + " gold"
	
	return tooltip

func can_stack_with(other_item: Item) -> bool:
	if item_type != ItemType.CONSUMABLE and item_type != ItemType.MATERIAL:
		return false
	
	return (item_name == other_item.item_name and 
			rarity == other_item.rarity and
			stat_modifiers == other_item.stat_modifiers)

func duplicate_item() -> Item:
	var new_item = Item.new()
	new_item.item_name = item_name
	new_item.description = description
	new_item.item_type = item_type
	new_item.rarity = rarity
	new_item.icon = icon
	new_item.value = value
	new_item.stat_modifiers = stat_modifiers.duplicate()
	new_item.special_modifiers = special_modifiers.duplicate()
	new_item.equipment_slot = equipment_slot
	new_item.level_requirement = level_requirement
	new_item.consumable_effect = consumable_effect
	new_item.effect_value = effect_value
	new_item.stack_size = stack_size
	new_item.is_craftable = is_craftable
	new_item.crafting_materials = crafting_materials.duplicate()
	
	return new_item
